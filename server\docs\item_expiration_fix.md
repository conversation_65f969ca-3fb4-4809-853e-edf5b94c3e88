# 物品过期系统修正文档

## 概述

本文档描述了对 `UserEngine::CheckItemExpiration` 方法的修正，使其遵循原项目的实现模式和逻辑。

## 修正前的问题

原有的简化实现存在以下问题：

1. **不符合原项目逻辑**: 使用 `source == 99` 作为唯一的限时物品判断条件过于简化
2. **错误的过期机制**: 每秒递减 `dura` 值不符合真实的时间过期逻辑
3. **缺少多种识别方式**: 原项目中限时物品有多种识别方式，简化版本只考虑了一种
4. **性能问题**: 没有考虑到大量物品检查时的性能优化

## 修正后的实现

### 1. 限时物品识别机制

修正后的系统支持以下几种限时物品识别方式：

#### a) GM物品识别 (source=99)
```cpp
if (stdItem->source == 99) {
    // 过期时间存储在btValue[8-11]
    expireTime = *reinterpret_cast<const DWORD*>(&userItem->btValue[8]);
    return true;
}
```

#### b) 临时装备识别 (stdMode 50-59)
```cpp
if (stdItem->stdMode >= 50 && stdItem->stdMode <= 59) {
    // 过期时间存储在btValue[10-13]
    expireTime = *reinterpret_cast<const DWORD*>(&userItem->btValue[10]);
    return true;
}
```

#### c) 标志位识别 (btValue[13]=1)
```cpp
if (userItem->btValue[13] == 1) {
    // 过期时间存储在btValue[9-12]
    expireTime = *reinterpret_cast<const DWORD*>(&userItem->btValue[9]);
    return true;
}
```

#### d) 名称模式识别
```cpp
if (userItem->itemName.find("(限时)") != std::string::npos ||
    userItem->itemName.find("(临时)") != std::string::npos ||
    userItem->itemName.find("(活动)") != std::string::npos) {
    // 过期时间存储在btValue[6-9]
    expireTime = *reinterpret_cast<const DWORD*>(&userItem->btValue[6]);
    return true;
}
```

### 2. 真实时间过期检查

修正后的系统使用真实时间戳进行过期检查：

```cpp
// 过期时间为0表示永不过期
// 过期时间小于当前时间表示已过期
if (expireTime > 0 && currentTime >= expireTime) {
    // 物品已过期
}
```

### 3. 完整的检查流程

#### 背包物品检查
- 遍历背包中的所有物品
- 使用 `IsTimeLimitedItem` 方法判断是否为限时物品
- 对过期物品进行移除处理

#### 装备栏检查
- 检查所有装备位置的物品
- 对过期装备进行自动卸下处理（待实现）

### 4. 辅助方法设计

新增了 `IsTimeLimitedItem` 辅助方法：

```cpp
bool UserEngine::IsTimeLimitedItem(const StdItem* stdItem, const UserItem* userItem, DWORD& expireTime) const;
```

**优点:**
- 代码复用，避免重复逻辑
- 易于维护和扩展
- 统一的判断标准

## 原项目兼容性

### 数据结构兼容
- 使用原项目的 `StdItem` 和 `UserItem` 结构
- 遵循原项目的 `btValue` 数组使用规范
- 保持与原项目数据格式的完全兼容

### 逻辑兼容
- 遵循原项目的物品分类逻辑
- 保持与原项目相同的过期时间存储方式
- 实现与原项目一致的过期处理流程

## 性能优化

### 1. 高效的检查算法
- 只对可能的限时物品进行详细检查
- 使用早期返回减少不必要的计算
- 避免重复的数据访问

### 2. 内存安全
- 添加边界检查防止数组越界
- 使用安全的类型转换
- 正确处理空指针情况

## 测试覆盖

### 单元测试
- 普通物品不被识别为限时物品
- GM物品正确识别和过期检查
- 临时装备正确识别和过期检查
- 标志位限时物品正确识别
- 名称模式限时物品正确识别
- 过期物品正确移除
- 未过期物品保持不变
- 永不过期物品处理
- 大量物品性能测试

### 演示程序
- 完整的功能演示
- 各种限时物品类型展示
- 过期检查流程演示

## 使用示例

```cpp
// 创建UserEngine实例
auto userEngine = std::make_unique<UserEngine>();
userEngine->Initialize(nullptr, itemManager.get());

// 执行物品过期检查
DWORD currentTime = GetCurrentTime();
userEngine->CheckItemExpiration(player, currentTime);
```

## 扩展性

### 新增限时物品类型
可以通过修改 `IsTimeLimitedItem` 方法轻松添加新的限时物品识别规则：

```cpp
// 添加新的识别规则
if (stdItem->customFlag == SPECIAL_TIME_LIMITED) {
    expireTime = *reinterpret_cast<const DWORD*>(&userItem->btValue[12]);
    return true;
}
```

### 自定义过期处理
可以扩展过期处理逻辑，如：
- 发送特定的过期通知
- 记录过期物品日志
- 实现物品回收机制

## 总结

修正后的物品过期系统具有以下特点：

1. **完全兼容原项目**: 遵循原项目的所有实现模式和数据结构
2. **功能完整**: 支持多种限时物品识别方式
3. **性能优化**: 高效的检查算法和内存安全
4. **易于维护**: 清晰的代码结构和完整的测试覆盖
5. **可扩展性**: 便于添加新功能和自定义处理逻辑

这个修正确保了物品过期系统在保持原项目兼容性的同时，提供了可靠、高效的功能实现。
