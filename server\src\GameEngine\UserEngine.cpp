// UserEngine.cpp - 用户引擎实现
#include "UserEngine.h"
#include "MapManager.h"
#include "ItemManager.h"
#include "MagicManager.h"
#include "StorageManager.h"
#include "TradeManager.h"
#include "QuestManager.h"
#include "MiniMapManager.h"
#include "RepairManager.h"
#include "PKManager.h"
#include "GroupManager.h"
#include "GuildManager.h"
#include "CastleManager.h"
#include "../Common/Logger.h"
#include <algorithm>
#include <sstream>
#include <chrono>

namespace MirServer {

// 全局实例
std::unique_ptr<UserEngine> g_UserEngine;

UserEngine::UserEngine() {
}

UserEngine::~UserEngine() {
    Finalize();
}

bool UserEngine::Initialize(std::shared_ptr<MapManager> mapManager,
                          std::shared_ptr<ItemManager> itemManager,
                          std::shared_ptr<MagicManager> magicManager,
                          std::shared_ptr<StorageManager> storageManager,
                          std::shared_ptr<TradeManager> tradeManager,
                          std::shared_ptr<QuestManager> questManager,
                          std::shared_ptr<MiniMapManager> miniMapManager,
                          std::shared_ptr<RepairManager> repairManager) {
    if (m_initialized) {
        return true;
    }

    // 设置必需的管理器
    m_mapManager = mapManager;
    m_itemManager = itemManager;

    if (!m_mapManager || !m_itemManager) {
        Logger::Error("UserEngine: Invalid map or item manager");
        return false;
    }

    // 设置可选的管理器
    m_magicManager = magicManager;
    m_storageManager = storageManager;
    m_tradeManager = tradeManager;
    m_questManager = questManager;
    m_miniMapManager = miniMapManager;
    m_repairManager = repairManager;

    // 清空数据
    m_players.clear();
    m_connectionMap.clear();

    // 重置统计
    m_statistics = Statistics();

    // 注册事件处理器到各个管理器
    RegisterEventHandlers();

    m_initialized = true;
    Logger::Info("UserEngine initialized successfully");
    return true;
}

void UserEngine::Finalize() {
    if (!m_initialized) {
        return;
    }

    // 保存所有玩家
    SaveAllPlayers();

    // 踢出所有玩家
    KickAllPlayers("Server shutdown");

    // 清理数据
    {
        std::unique_lock<std::shared_mutex> lock(m_playersMutex);
        m_players.clear();
        m_connectionMap.clear();
    }

    m_initialized = false;
    Logger::Info("UserEngine finalized");
}

std::shared_ptr<PlayObject> UserEngine::CreatePlayer(const HumDataInfo& humData) {
    auto player = std::make_shared<PlayObject>();

    // 设置基本信息
    player->SetCharName(humData.charName);
    player->SetJob(humData.job);
    player->SetGender(humData.gender);
    player->SetLevel(humData.level);
    player->SetDirection(humData.direction);
    player->SetCurrentPos(humData.currentPos);
    player->SetMapName(humData.mapName);
    player->SetGold(humData.gold);

    // 初始化其他属性
    player->Initialize();

    return player;
}

bool UserEngine::AddPlayer(std::shared_ptr<PlayObject> player) {
    if (!player) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_playersMutex);

    // 检查是否已存在
    if (m_players.find(player->GetCharName()) != m_players.end()) {
        Logger::Error("Player already exists: " + player->GetCharName());
        return false;
    }

    // 添加到列表
    m_players[player->GetCharName()] = player;

    // 更新统计
    {
        std::lock_guard<std::mutex> statsLock(m_statsMutex);
        m_statistics.totalPlayers++;
        m_statistics.activePlayers++;
        if (m_statistics.activePlayers > m_statistics.maxConcurrentPlayers) {
            m_statistics.maxConcurrentPlayers = m_statistics.activePlayers;
        }
    }

    Logger::Info("Player added: " + player->GetCharName());
    return true;
}

bool UserEngine::RemovePlayer(const std::string& charName) {
    std::unique_lock<std::shared_mutex> lock(m_playersMutex);

    auto it = m_players.find(charName);
    if (it == m_players.end()) {
        return false;
    }

    m_players.erase(it);

    // 更新统计
    {
        std::lock_guard<std::mutex> statsLock(m_statsMutex);
        if (m_statistics.activePlayers > 0) {
            m_statistics.activePlayers--;
        }
    }

    Logger::Info("Player removed: " + charName);
    return true;
}

std::shared_ptr<PlayObject> UserEngine::GetPlayer(const std::string& charName) const {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);

    auto it = m_players.find(charName);
    if (it != m_players.end()) {
        return it->second;
    }

    return nullptr;
}

std::shared_ptr<PlayObject> UserEngine::GetPlayerByConnection(uint32_t connectionId) const {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);

    auto it = m_connectionMap.find(connectionId);
    if (it != m_connectionMap.end()) {
        return it->second;
    }

    return nullptr;
}

std::vector<std::shared_ptr<PlayObject>> UserEngine::GetAllPlayers() const {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);

    std::vector<std::shared_ptr<PlayObject>> result;
    result.reserve(m_players.size());

    for (const auto& pair : m_players) {
        result.push_back(pair.second);
    }

    return result;
}

size_t UserEngine::GetPlayerCount() const {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);
    return m_players.size();
}

bool UserEngine::BindPlayerToConnection(std::shared_ptr<PlayObject> player, uint32_t connectionId) {
    if (!player) {
        return false;
    }

    std::unique_lock<std::shared_mutex> lock(m_playersMutex);

    // 移除旧的连接绑定
    for (auto it = m_connectionMap.begin(); it != m_connectionMap.end(); ) {
        if (it->second == player) {
            it = m_connectionMap.erase(it);
        } else {
            ++it;
        }
    }

    // 绑定新连接
    m_connectionMap[connectionId] = player;
    player->SetConnectionId(connectionId);

    return true;
}

void UserEngine::UnbindPlayerConnection(uint32_t connectionId) {
    std::unique_lock<std::shared_mutex> lock(m_playersMutex);

    auto it = m_connectionMap.find(connectionId);
    if (it != m_connectionMap.end()) {
        if (it->second) {
            it->second->SetConnectionId(0);
        }
        m_connectionMap.erase(it);
    }
}

bool UserEngine::PlayerLogin(std::shared_ptr<PlayObject> player) {
    if (!player) {
        return false;
    }

    // 添加玩家
    if (!AddPlayer(player)) {
        return false;
    }

    // 更新统计
    {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_statistics.totalLoginCount++;
    }

    // 触发登录事件
    if (m_onPlayerLogin) {
        m_onPlayerLogin(player);
    }

    Logger::Info("Player login: " + player->GetCharName());
    return true;
}

bool UserEngine::PlayerLogout(const std::string& charName) {
    auto player = GetPlayer(charName);
    if (!player) {
        return false;
    }

    // 保存玩家数据
    player->SaveData();

    // 更新统计
    {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_statistics.totalLogoutCount++;
    }

    // 触发登出事件
    if (m_onPlayerLogout) {
        m_onPlayerLogout(player);
    }

    // 解绑连接
    if (player->GetConnectionId() > 0) {
        UnbindPlayerConnection(player->GetConnectionId());
    }

    // 移除玩家
    RemovePlayer(charName);

    Logger::Info("Player logout: " + charName);
    return true;
}

void UserEngine::PlayerDisconnect(uint32_t connectionId) {
    auto player = GetPlayerByConnection(connectionId);
    if (player) {
        PlayerLogout(player->GetCharName());
    } else {
        UnbindPlayerConnection(connectionId);
    }
}

void UserEngine::BroadcastMessage(const std::string& message, BYTE color) {
    auto players = GetAllPlayers();
    for (const auto& player : players) {
        player->SendMessage(message, color);
    }
}

void UserEngine::BroadcastSystemMessage(const std::string& message) {
    BroadcastMessage("[System] " + message, 0xFF);
}

void UserEngine::BroadcastPacket(const std::vector<uint8_t>& packet) {
    auto players = GetAllPlayers();
    for (const auto& player : players) {
        player->SendPacket(packet);
    }
}

void UserEngine::BroadcastPacketInRange(const Point& center, int range, const std::string& mapName,
                                       const std::vector<uint8_t>& packet) {
    auto players = GetAllPlayers();
    for (const auto& player : players) {
        if (player->GetMapName() == mapName) {
            if (GetDistance(player->GetCurrentPos(), center) <= range) {
                player->SendPacket(packet);
            }
        }
    }
}

void UserEngine::Run() {
    DWORD currentTime = GetCurrentTime();

    // 处理玩家
    ProcessPlayers();

    // 检查超时
    if (currentTime - m_lastCheckTime >= m_timeoutCheckInterval) {
        CheckPlayerTimeOut();
        m_lastCheckTime = currentTime;
    }

    // 自动保存
    if (currentTime - m_lastSaveTime >= m_autoSaveInterval) {
        SaveAllPlayers();
        m_lastSaveTime = currentTime;
    }

    // 处理定时任务
    {
        std::lock_guard<std::mutex> lock(m_timerMutex);
        for (auto& task : m_timerTasks) {
            if (currentTime - task.lastRunTime >= task.interval) {
                task.task();
                task.lastRunTime = currentTime;
            }
        }
    }
}

void UserEngine::ProcessPlayers() {
    auto players = GetAllPlayers();

    for (const auto& player : players) {
        if (!player || !player->IsAlive()) {
            continue;
        }

        // 处理玩家数据包
        ProcessPlayerPackets(player);

        // 处理移动
        HandlePlayerMovement(player);

        // 处理战斗
        HandlePlayerCombat(player);

        // 处理物品
        HandlePlayerItems(player);

        // 处理交易
        HandlePlayerTrade(player);

        // 处理组队
        HandlePlayerGroup(player);

        // 处理行会
        HandlePlayerGuild(player);

        // 处理魔法
        HandlePlayerMagic(player);

        // 处理任务
        HandlePlayerQuest(player);

        // 处理仓库
        HandlePlayerStorage(player);

        // 处理修理
        HandlePlayerRepair(player);

        // 更新环境
        UpdatePlayerEnvironment(player);

        // 运行玩家逻辑
        player->Run();
    }
}

void UserEngine::CheckPlayerTimeOut() {
    auto players = GetAllPlayers();
    DWORD currentTime = GetCurrentTime();

    for (const auto& player : players) {
        if (player && player->GetConnectionId() > 0) {
            // 检查超时
            if (currentTime - player->GetLastActiveTime() > m_playerTimeout) {
                Logger::Warning("Player timeout: " + player->GetCharName());
                PlayerDisconnect(player->GetConnectionId());
            }
        }
    }
}

void UserEngine::SaveAllPlayers() {
    auto players = GetAllPlayers();

    for (const auto& player : players) {
        if (player) {
            player->SaveData();
        }
    }

    Logger::Info("All players saved");
}

UserEngine::Statistics UserEngine::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    return m_statistics;
}

bool UserEngine::ProcessGMCommand(std::shared_ptr<PlayObject> player, const std::string& command) {
    if (!player || !player->IsGM()) {
        return false;
    }

    // 解析命令
    std::istringstream iss(command);
    std::string cmd;
    std::vector<std::string> args;

    iss >> cmd;
    std::string arg;
    while (iss >> arg) {
        args.push_back(arg);
    }

    // 转换为小写
    std::transform(cmd.begin(), cmd.end(), cmd.begin(), ::tolower);

    // 处理命令
    if (cmd == "@level") {
        GMCommand_Level(player, args);
    } else if (cmd == "@gold") {
        GMCommand_Gold(player, args);
    } else if (cmd == "@item") {
        GMCommand_Item(player, args);
    } else if (cmd == "@move") {
        GMCommand_Move(player, args);
    } else if (cmd == "@kick") {
        GMCommand_Kick(player, args);
    } else if (cmd == "@shutdown") {
        GMCommand_Shutdown(player, args);
    } else {
        player->SendMessage("Unknown command: " + cmd);
        return false;
    }

    return true;
}

void UserEngine::KickPlayer(const std::string& charName, const std::string& reason) {
    auto player = GetPlayer(charName);
    if (player) {
        player->SendMessage("You have been kicked: " + reason);
        PlayerLogout(charName);
    }
}

void UserEngine::KickAllPlayers(const std::string& reason) {
    auto players = GetAllPlayers();

    for (const auto& player : players) {
        if (player) {
            player->SendMessage("Server is shutting down: " + reason);
        }
    }

    // 清空所有玩家
    {
        std::unique_lock<std::shared_mutex> lock(m_playersMutex);
        m_players.clear();
        m_connectionMap.clear();
    }
}

bool UserEngine::IsPlayerOnline(const std::string& charName) const {
    return GetPlayer(charName) != nullptr;
}

// 内部方法实现
void UserEngine::ProcessPlayerPackets(std::shared_ptr<PlayObject> player) {
    // 这里处理玩家的网络数据包
    // 具体实现依赖于网络模块
    if (!player) return;

    // 处理玩家的消息队列 - 通过Run方法处理
    // player->Run() 会调用内部的ProcessMessages()
}

void UserEngine::HandlePlayerMovement(std::shared_ptr<PlayObject> player) {
    if (!player || !m_mapManager) return;

    DWORD currentTime = GetCurrentTime();

    // 处理玩家移动逻辑
    ProcessPlayerMovementLogic(player, currentTime);

    // 检查地图边界和安全区
    CheckPlayerMapBoundary(player);

    // 处理传送点检测
    CheckPlayerTeleportPoints(player);

    // 更新玩家在地图上的位置信息
    if (m_mapManager->MapExists(player->GetMapName())) {
        // 通知地图管理器更新玩家位置
        // m_mapManager->UpdatePlayerPosition(player);
    }
}

void UserEngine::HandlePlayerCombat(std::shared_ptr<PlayObject> player) {
    if (!player) return;

    DWORD currentTime = GetCurrentTime();

    // 处理玩家战斗逻辑
    ProcessPlayerCombatLogic(player, currentTime);

    // 检查PK状态和相关处理
    auto& pkManager = PKManager::GetInstance();
    if (pkManager.IsRedName(player.get())) {
        // 红名玩家的特殊处理
        CheckRedNamePenalty(player, currentTime);
    }

    // 处理战斗状态检查
    CheckPlayerCombatState(player, currentTime);

    // 处理攻击冷却时间
    CheckPlayerAttackCooldown(player, currentTime);
}

void UserEngine::HandlePlayerItems(std::shared_ptr<PlayObject> player) {
    if (!player || !m_itemManager) return;

    DWORD currentTime = GetCurrentTime();

    // 检查装备耐久度损耗
    CheckPlayerItemDurability(player, currentTime);

    // 处理物品自动使用（如自动喝药）
    ProcessAutoUseItems(player, currentTime);

    // 检查物品过期
    CheckItemExpiration(player, currentTime);

    // 处理物品特殊效果
    ProcessItemEffects(player, currentTime);
}

void UserEngine::HandlePlayerTrade(std::shared_ptr<PlayObject> player) {
    if (!player || !m_tradeManager) return;

    // 检查交易状态
    if (m_tradeManager->IsInTrade(player.get())) {
        // 获取交易数据
        const TradeData* tradeData = m_tradeManager->GetTradeData(player.get());
        if (tradeData) {
            // 检查交易超时
            DWORD currentTime = GetCurrentTime();
            if (currentTime - tradeData->startTime > 300000) { // 5分钟超时
                m_tradeManager->CancelTrade(player.get());
                player->SendMessage("交易超时，已自动取消", 2);
            }

            // 检查交易伙伴是否在线
            PlayObject* partner = m_tradeManager->GetTradePartner(player.get());
            if (!partner || !IsPlayerOnline(partner->GetCharName())) {
                m_tradeManager->CancelTrade(player.get());
                player->SendMessage("交易伙伴已离线，交易取消", 2);
            }
        }
    }
}

void UserEngine::HandlePlayerGroup(std::shared_ptr<PlayObject> player) {
    if (!player) return;

    // 处理组队相关逻辑
    auto& groupManager = GroupManager::GetInstance();

    // 检查组队状态
    if (groupManager.IsInGroup(player.get())) {
        // 更新组队成员位置信息
        groupManager.UpdateGroupPositions(groupManager.GetPlayerGroup(player.get())->groupId);
    }
}

void UserEngine::HandlePlayerGuild(std::shared_ptr<PlayObject> player) {
    if (!player) return;

    // 处理行会相关逻辑
    auto* guild = GuildManager::GetInstance().GetPlayerGuild(player->GetCharName());
    if (guild) {
        // 处理行会相关的定时任务
        // 如行会战、行会活动等
    }
}

void UserEngine::HandlePlayerMagic(std::shared_ptr<PlayObject> player) {
    if (!player || !m_magicManager) return;

    DWORD currentTime = GetCurrentTime();

    // 处理魔法相关逻辑
    ProcessPlayerMagicLogic(player, currentTime);

    // 检查魔法冷却时间
    CheckPlayerMagicCooldown(player, currentTime);

    // 处理持续性魔法效果
    ProcessPlayerMagicEffects(player, currentTime);

    // 检查魔法值恢复
    CheckPlayerMPRegeneration(player, currentTime);
}

void UserEngine::HandlePlayerQuest(std::shared_ptr<PlayObject> player) {
    if (!player || !m_questManager) return;

    // 获取玩家任务列表
    auto playerQuests = m_questManager->GetPlayerQuests(player.get());

    // 检查任务进度和完成状态
    for (auto& questStatus : playerQuests) {
        if (questStatus.state == QuestState::ACCEPTED) {
            // 检查任务是否可以完成
            const QuestData* questData = m_questManager->GetQuest(questStatus.questId);
            if (questData && CheckQuestCompletion(player, questData)) {
                // 标记任务为可完成状态
                questStatus.state = QuestState::COMPLETED;
                player->SendMessage("任务 " + questData->name + " 已完成，请找NPC交任务", 0);
            }
        }
    }

    // 检查任务超时
    CheckQuestTimeouts(player);
}

void UserEngine::HandlePlayerStorage(std::shared_ptr<PlayObject> player) {
    if (!player || !m_storageManager) return;

    // 检查仓库会话超时
    if (m_storageManager->IsStorageOpen(player.get())) {
        // 仓库会话管理在StorageManager中处理
        // 这里可以添加额外的检查逻辑，如超时检测等
        DWORD currentTime = GetCurrentTime();
        // 假设仓库会话有30分钟超时
        // 具体的超时逻辑应该在StorageManager中实现
    }
}

void UserEngine::HandlePlayerRepair(std::shared_ptr<PlayObject> player) {
    if (!player || !m_repairManager) return;

    // 处理修理相关逻辑
    // 检查是否有修理会话正在进行
    // 具体的修理逻辑在RepairManager中实现
    // 这里主要做状态检查和清理工作
}

void UserEngine::UpdatePlayerEnvironment(std::shared_ptr<PlayObject> player) {
    if (!player) return;

    DWORD currentTime = GetCurrentTime();

    // 更新玩家视野范围
    UpdatePlayerViewRange(player, currentTime);

    // 发送周围对象信息
    SendAroundObjects(player);

    // 检查地图环境效果
    CheckMapEnvironmentEffects(player, currentTime);

    // 更新玩家状态（如中毒、BUFF等）
    UpdatePlayerStatusEffects(player, currentTime);
}

void UserEngine::SendAroundObjects(std::shared_ptr<PlayObject> player) {
    if (!player) return;

    // 获取玩家周围的其他玩家
    Point playerPos = player->GetCurrentPos();
    std::string mapName = player->GetMapName();
    int viewRange = 12; // 视野范围

    auto allPlayers = GetAllPlayers();
    for (const auto& otherPlayer : allPlayers) {
        if (!otherPlayer || otherPlayer == player) continue;

        // 检查是否在同一地图
        if (otherPlayer->GetMapName() != mapName) continue;

        // 检查距离
        Point otherPos = otherPlayer->GetCurrentPos();
        if (GetDistance(playerPos, otherPos) <= viewRange) {
            // 发送其他玩家信息给当前玩家
            player->SendRefMsg(Protocol::SM_USERNAME,
                             otherPlayer->GetObjectId(),
                             otherPos.x, otherPos.y,
                             static_cast<WORD>(otherPlayer->GetDirection()),
                             otherPlayer->GetCharName());
        }
    }
}

// GM命令实现
void UserEngine::GMCommand_Level(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    if (args.empty()) {
        player->SendMessage("Usage: @level <level>");
        return;
    }

    int level = std::stoi(args[0]);
    if (level < 1 || level > 65535) {
        player->SendMessage("Invalid level");
        return;
    }

    player->SetLevel(level);
    player->SendMessage("Level set to " + std::to_string(level));
}

void UserEngine::GMCommand_Gold(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    if (args.empty()) {
        player->SendMessage("Usage: @gold <amount>");
        return;
    }

    DWORD gold = std::stoul(args[0]);
    player->SetGold(gold);
    player->SendMessage("Gold set to " + std::to_string(gold));
}

void UserEngine::GMCommand_Item(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    if (args.empty()) {
        player->SendMessage("Usage: @item <itemname> [count]");
        return;
    }

    if (!m_itemManager) {
        player->SendMessage("ItemManager not available");
        return;
    }

    std::string itemName = args[0];
    int count = args.size() > 1 ? std::stoi(args[1]) : 1;

    if (count <= 0 || count > 100) {
        player->SendMessage("Invalid count (1-100)");
        return;
    }

    // 通过ItemManager查找物品（按名称）
    auto* stdItem = m_itemManager->GetStdItemByName(itemName);
    if (!stdItem) {
        player->SendMessage("Item not found: " + itemName);
        return;
    }

    // 给玩家添加物品
    for (int i = 0; i < count; ++i) {
        // 使用ItemManager创建物品
        UserItem userItem = m_itemManager->CreateItem(stdItem->idx);
        if (userItem.itemIndex == 0) {
            player->SendMessage("Failed to create item: " + itemName);
            break;
        }

        if (player->AddBagItem(userItem)) {
            player->SendMessage("Added item: " + itemName);
        } else {
            player->SendMessage("Bag is full");
            break;
        }
    }
}

void UserEngine::GMCommand_Move(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    if (args.size() < 3) {
        player->SendMessage("Usage: @move <mapname> <x> <y>");
        return;
    }

    if (!m_mapManager) {
        player->SendMessage("MapManager not available");
        return;
    }

    std::string mapName = args[0];
    int x = std::stoi(args[1]);
    int y = std::stoi(args[2]);

    // 检查坐标范围
    if (x < 0 || y < 0 || x > 1000 || y > 1000) {
        player->SendMessage("Invalid coordinates");
        return;
    }

    // 检查地图是否存在
    if (!m_mapManager->MapExists(mapName)) {
        player->SendMessage("Map not found: " + mapName);
        return;
    }

    // 执行传送
    if (player->SpaceMove(mapName, x, y)) {
        player->SendMessage("Moved to " + mapName + " (" + std::to_string(x) + "," + std::to_string(y) + ")");
    } else {
        player->SendMessage("Move failed");
    }
}

void UserEngine::GMCommand_Kick(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    if (args.empty()) {
        player->SendMessage("Usage: @kick <charname>");
        return;
    }

    std::string targetName = args[0];
    KickPlayer(targetName, "Kicked by GM");
    player->SendMessage("Player " + targetName + " kicked");
}

void UserEngine::GMCommand_Shutdown(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args) {
    int delay = args.empty() ? 0 : std::stoi(args[0]);

    if (delay > 0) {
        BroadcastSystemMessage("Server will shutdown in " + std::to_string(delay) + " seconds");
        // 这里需要实现延迟关闭
    } else {
        BroadcastSystemMessage("Server is shutting down now!");
        KickAllPlayers("Server shutdown");
    }
}

// 其他方法实现
std::shared_ptr<PlayObject> UserEngine::FindPlayer(std::function<bool(const std::shared_ptr<PlayObject>&)> predicate) const {
    auto players = GetAllPlayers();

    auto it = std::find_if(players.begin(), players.end(), predicate);
    if (it != players.end()) {
        return *it;
    }

    return nullptr;
}

std::vector<std::shared_ptr<PlayObject>> UserEngine::FindPlayers(std::function<bool(const std::shared_ptr<PlayObject>&)> predicate) const {
    auto players = GetAllPlayers();
    std::vector<std::shared_ptr<PlayObject>> result;

    std::copy_if(players.begin(), players.end(), std::back_inserter(result), predicate);

    return result;
}

std::vector<std::shared_ptr<PlayObject>> UserEngine::GetTopLevelPlayers(size_t count) const {
    auto players = GetAllPlayers();

    // 按等级排序
    std::sort(players.begin(), players.end(),
        [](const std::shared_ptr<PlayObject>& a, const std::shared_ptr<PlayObject>& b) {
            return a->GetHumDataInfo().level > b->GetHumDataInfo().level;
        });

    // 返回前N个
    if (players.size() > count) {
        players.resize(count);
    }

    return players;
}

std::vector<std::shared_ptr<PlayObject>> UserEngine::GetTopPKPlayers(size_t count) const {
    auto players = GetAllPlayers();

    // 按PK点数排序
    std::sort(players.begin(), players.end(),
        [](const std::shared_ptr<PlayObject>& a, const std::shared_ptr<PlayObject>& b) {
            return a->GetHumDataInfo().pkPoint > b->GetHumDataInfo().pkPoint;
        });

    // 返回前N个
    if (players.size() > count) {
        players.resize(count);
    }

    return players;
}

void UserEngine::RegisterTimerTask(const std::string& name, std::function<void()> task, DWORD interval) {
    std::lock_guard<std::mutex> lock(m_timerMutex);

    // 移除同名任务
    m_timerTasks.erase(
        std::remove_if(m_timerTasks.begin(), m_timerTasks.end(),
            [&name](const TimerTask& t) { return t.name == name; }),
        m_timerTasks.end()
    );

    // 添加新任务
    TimerTask newTask;
    newTask.name = name;
    newTask.task = task;
    newTask.interval = interval;
    newTask.lastRunTime = GetCurrentTime();

    m_timerTasks.push_back(newTask);
}

void UserEngine::UnregisterTimerTask(const std::string& name) {
    std::lock_guard<std::mutex> lock(m_timerMutex);

    m_timerTasks.erase(
        std::remove_if(m_timerTasks.begin(), m_timerTasks.end(),
            [&name](const TimerTask& t) { return t.name == name; }),
        m_timerTasks.end()
    );
}

// 注册事件处理器
void UserEngine::RegisterEventHandlers() {
    // 设置玩家登录事件处理器
    SetOnPlayerLogin([this](std::shared_ptr<PlayObject> player) {
        // 通知PK管理器
        PKManager::GetInstance().OnPlayerLogin(player.get());

        // 通知组队管理器
        GroupManager::GetInstance().OnPlayerLogin(player.get());

        // 通知行会管理器 - 通过Guild对象处理
        auto* guild = GuildManager::GetInstance().GetPlayerGuild(player->GetCharName());
        if (guild) {
            guild->OnPlayerLogin(player.get());
        }

        Logger::Info("Player login events processed: " + player->GetCharName());
    });

    // 设置玩家登出事件处理器
    SetOnPlayerLogout([this](std::shared_ptr<PlayObject> player) {
        // 通知PK管理器
        PKManager::GetInstance().OnPlayerLogout(player.get());

        // 通知组队管理器
        GroupManager::GetInstance().OnPlayerLogout(player.get());

        // 通知行会管理器 - 通过Guild对象处理
        auto* guild = GuildManager::GetInstance().GetPlayerGuild(player->GetCharName());
        if (guild) {
            guild->OnPlayerLogout(player.get());
        }

        // 清理交易状态
        if (m_tradeManager) {
            m_tradeManager->CancelTrade(player.get());
        }

        // 清理仓库状态
        if (m_storageManager) {
            m_storageManager->CloseStorage(player.get());
        }

        Logger::Info("Player logout events processed: " + player->GetCharName());
    });

    // 设置玩家死亡事件处理器
    SetOnPlayerDeath([this](std::shared_ptr<PlayObject> player) {
        // 通知PK管理器
        PKManager::GetInstance().OnPlayerDeath(player.get());

        Logger::Info("Player death events processed: " + player->GetCharName());
    });

    // 设置玩家升级事件处理器
    SetOnPlayerLevelUp([this](std::shared_ptr<PlayObject> player) {
        // 广播升级消息
        BroadcastSystemMessage(player->GetCharName() + " has reached level " +
                              std::to_string(player->GetLevel()) + "!");

        Logger::Info("Player level up events processed: " + player->GetCharName());
    });
}

// Handle方法的辅助方法实现
void UserEngine::CheckPlayerItemDurability(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player || !m_itemManager) return;

    // 检查装备耐久度
    for (int i = 0; i < static_cast<int>(EquipPosition::MAX_EQUIP); i++) {
        const UserItem* equipItem = player->GetEquipItem(static_cast<EquipPosition>(i));
        if (equipItem && equipItem->itemIndex > 0) {
            // 获取物品标准信息
            const StdItem* stdItem = m_itemManager->GetStdItem(equipItem->itemIndex);
            if (stdItem) {
                // 检查耐久度是否过低
                if (equipItem->dura <= stdItem->duraMax / 10) { // 耐久度低于10%
                    player->SendMessage("装备 " + equipItem->itemName + " 耐久度过低，请及时修理！", 2);
                }

                // 检查是否完全损坏
                if (equipItem->dura <= 0) {
                    player->SendMessage("装备 " + equipItem->itemName + " 已损坏！", 2);
                    // TODO: 可以考虑自动卸下损坏的装备
                }
            }
        }
    }
}

void UserEngine::ProcessAutoUseItems(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player || !m_itemManager) return;

    // 检查是否需要自动使用药水（简化实现）
    const auto& ability = player->GetAbility();

    // 生命值低于30%时自动使用红药
    if (player->GetHP() < ability.HP * 30 / 100) {
        const auto& bagItems = player->GetBagItems();
        for (const auto& item : bagItems) {
            // 查找红药水（假设itemIndex 1000-1010为红药水）
            if (item.itemIndex >= 1000 && item.itemIndex <= 1010) {
                if (player->UseItem(item.makeIndex)) {
                    break; // 使用一个就够了
                }
            }
        }
    }

    // 魔法值低于30%时自动使用蓝药
    if (player->GetMP() < ability.MP * 30 / 100) {
        const auto& bagItems = player->GetBagItems();
        for (const auto& item : bagItems) {
            // 查找蓝药水（假设itemIndex 1020-1030为蓝药水）
            if (item.itemIndex >= 1020 && item.itemIndex <= 1030) {
                if (player->UseItem(item.makeIndex)) {
                    break; // 使用一个就够了
                }
            }
        }
    }
}

// 物品过期检查辅助方法实现
bool UserEngine::IsTimeLimitedItem(const StdItem* stdItem, const UserItem* userItem, DWORD& expireTime) const {
    if (!stdItem || !userItem) {
        expireTime = 0;
        return false;
    }

    // 根据原项目逻辑，以下情况表示限时物品：
    // 1. source为特定值（如99表示GM物品，可能有时间限制）
    // 2. 特定的物品类型（如活动物品、临时装备等）
    // 3. btValue[13]存储过期时间标志

    // 检查source值（原项目中source=99通常表示特殊物品）
    if (stdItem->source == 99) {
        // 在btValue[8-11]中存储过期时间（DWORD，4字节）
        expireTime = *reinterpret_cast<const DWORD*>(&userItem->btValue[8]);
        return true;
    }

    // 检查特定物品类型（如临时装备、活动物品）
    // 在原项目中，某些stdMode值表示临时物品
    if (stdItem->stdMode >= 50 && stdItem->stdMode <= 59) {
        // 对于这类物品，过期时间存储在btValue[10-13]
        // 注意：需要确保不会越界，btValue只有14个字节
        if (sizeof(userItem->btValue) >= 14) {
            expireTime = *reinterpret_cast<const DWORD*>(&userItem->btValue[10]);
        } else {
            expireTime = 0;
        }
        return true;
    }

    // 检查btValue[13]标志位（原项目中用于标识限时物品）
    if (userItem->btValue[13] == 1) {
        // 过期时间存储在btValue[9-12]
        expireTime = *reinterpret_cast<const DWORD*>(&userItem->btValue[9]);
        return true;
    }

    // 检查特殊的物品名称模式（原项目中某些物品名称包含时间限制信息）
    if (userItem->itemName.find("(限时)") != std::string::npos ||
        userItem->itemName.find("(临时)") != std::string::npos ||
        userItem->itemName.find("(活动)") != std::string::npos) {
        // 对于这类物品，过期时间可能存储在btValue[6-9]
        expireTime = *reinterpret_cast<const DWORD*>(&userItem->btValue[6]);
        return true;
    }

    expireTime = 0;
    return false;
}

void UserEngine::CheckItemExpiration(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player || !m_itemManager) return;

    // 检查背包中的限时物品
    auto& bagItems = const_cast<std::vector<UserItem>&>(player->GetBagItems());
    for (auto it = bagItems.begin(); it != bagItems.end(); ) {
        const StdItem* stdItem = m_itemManager->GetStdItem(it->itemIndex);
        if (stdItem) {
            DWORD expireTime = 0;
            if (IsTimeLimitedItem(stdItem, &(*it), expireTime)) {
                // 如果是限时物品且有过期时间，检查是否过期
                if (expireTime > 0 && currentTime >= expireTime) {
                    player->SendMessage("物品 " + it->itemName + " 已过期并被移除", 2);
                    it = bagItems.erase(it);
                    continue;
                }
            }
        }
        ++it;
    }

    // 检查装备栏中的限时物品
    for (int i = 0; i < static_cast<int>(EquipPosition::MAX_EQUIP); i++) {
        const UserItem* equipItem = player->GetEquipItem(static_cast<EquipPosition>(i));
        if (equipItem && equipItem->itemIndex > 0) {
            const StdItem* stdItem = m_itemManager->GetStdItem(equipItem->itemIndex);
            if (stdItem) {
                DWORD expireTime = 0;
                if (IsTimeLimitedItem(stdItem, equipItem, expireTime)) {
                    // 如果装备过期，自动卸下
                    if (expireTime > 0 && currentTime >= expireTime) {
                        player->SendMessage("装备 " + equipItem->itemName + " 已过期并被自动卸下", 2);
                        // TODO: 实现自动卸下装备的逻辑
                        // player->TakeOffItem(static_cast<EquipPosition>(i));
                    }
                }
            }
        }
    }
}

void UserEngine::ProcessItemEffects(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player || !m_itemManager) return;

    // 处理装备的特殊效果
    for (int i = 0; i < static_cast<int>(EquipPosition::MAX_EQUIP); i++) {
        const UserItem* equipItem = player->GetEquipItem(static_cast<EquipPosition>(i));
        if (equipItem && equipItem->itemIndex > 0) {
            const StdItem* stdItem = m_itemManager->GetStdItem(equipItem->itemIndex);
            if (stdItem) {
                // 处理装备的持续效果
                // 例如：回血装备、回蓝装备、特殊属性加成等
                // 这里需要根据具体的装备系统实现

                // 示例：假设某些装备有回血效果
                if (stdItem->ac > 0 && currentTime % 5000 == 0) { // 每5秒触发一次
                    int healAmount = stdItem->ac / 10; // 根据防御力计算回血量
                    if (healAmount > 0) {
                        player->Heal(healAmount);
                    }
                }
            }
        }
    }
}

// 移动相关辅助方法实现
void UserEngine::ProcessPlayerMovementLogic(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player) return;

    // 检查玩家是否处于移动状态
    // 这里可以处理自动寻路、跟随等逻辑

    // 检查移动速度限制（使用BaseObject的方法）
    if (currentTime - player->GetLastActiveTime() < 100) { // 100ms移动间隔
        // 移动太频繁，可能是外挂
        // 可以记录日志或采取相应措施
        Logger::Warning("Player " + player->GetCharName() + " moving too fast");
    }

    // 处理传送冷却时间
    // 这里使用简化的检查，实际应该在PlayObject中维护传送时间
    static std::unordered_map<std::string, DWORD> lastSpaceMoveTime;
    auto it = lastSpaceMoveTime.find(player->GetCharName());
    if (it != lastSpaceMoveTime.end() && currentTime - it->second < 1000) {
        // 传送冷却中
    }
}

void UserEngine::CheckPlayerMapBoundary(std::shared_ptr<PlayObject> player) {
    if (!player || !m_mapManager) return;

    Point pos = player->GetCurrentPos();
    std::string mapName = player->GetMapName();

    // 检查是否超出地图边界
    // 这里需要MapManager提供地图边界信息
    // 如果超出边界，可以将玩家传送回安全位置

    // 检查是否在安全区
    // 如果在安全区，设置相应的状态
}

void UserEngine::CheckPlayerTeleportPoints(std::shared_ptr<PlayObject> player) {
    if (!player || !m_mapManager) return;

    Point pos = player->GetCurrentPos();
    std::string mapName = player->GetMapName();

    // 检查玩家是否踩到传送点
    // 这里需要MapManager提供传送点信息
    // 如果踩到传送点，执行传送逻辑
}

// 战斗相关辅助方法实现
void UserEngine::ProcessPlayerCombatLogic(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player) return;

    // 检查玩家是否在战斗状态（简化实现）
    // 实际应该在PlayObject中维护战斗状态
    static std::unordered_map<std::string, DWORD> playerCombatTime;
    auto it = playerCombatTime.find(player->GetCharName());
    if (it != playerCombatTime.end() && currentTime - it->second < 10000) {
        // 玩家在战斗状态中
        // 处理战斗状态下的逻辑
    }

    // 检查玩家生命值状态
    if (player->GetHP() <= 0) {
        // 玩家已死亡，清理战斗状态
        playerCombatTime.erase(player->GetCharName());
    }
}

void UserEngine::CheckRedNamePenalty(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player) return;

    // 红名玩家的惩罚处理
    DWORD pkPoint = player->GetPKPoint();
    if (pkPoint > 0) {
        // 红名玩家不能进入某些地图
        // 红名玩家死亡掉落更多物品
        // 红名玩家受到城镇守卫攻击等

        // 每分钟减少1点PK值（简化实现）
        static std::unordered_map<std::string, DWORD> lastPKDecreaseTime;
        auto it = lastPKDecreaseTime.find(player->GetCharName());
        if (it == lastPKDecreaseTime.end() || currentTime - it->second >= 60000) { // 1分钟
            if (pkPoint > 0) {
                player->IncPKPoint(-1);
                lastPKDecreaseTime[player->GetCharName()] = currentTime;
            }
        }
    }
}

void UserEngine::CheckPlayerCombatState(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player) return;

    // 检查战斗状态超时（简化实现）
    static std::unordered_map<std::string, DWORD> lastCombatTime;
    auto it = lastCombatTime.find(player->GetCharName());
    if (it != lastCombatTime.end() && currentTime - it->second > 10000) { // 10秒脱战
        lastCombatTime.erase(player->GetCharName());
    }

    // 检查玩家状态
    if (player->GetHP() < player->GetMaxHP() / 4) { // 生命值低于25%
        // 可以触发一些紧急状态的处理
    }
}

void UserEngine::CheckPlayerAttackCooldown(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player) return;

    // 检查攻击冷却时间（简化实现）
    static std::unordered_map<std::string, DWORD> lastAttackTime;
    auto it = lastAttackTime.find(player->GetCharName());
    if (it != lastAttackTime.end() && currentTime - it->second < player->GetAttackSpeed()) {
        // 攻击冷却中
        return;
    }

    // 检查技能冷却时间
    // 这里可以检查各种技能的冷却状态
}

// 魔法相关辅助方法实现
void UserEngine::ProcessPlayerMagicLogic(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player || !m_magicManager) return;

    // 处理玩家魔法相关逻辑
    // 检查魔法状态、冷却时间等

    // 检查魔法值恢复
    if (player->GetMP() < player->GetMaxMP()) {
        // 魔法值自然恢复（简化实现）
        static std::unordered_map<std::string, DWORD> lastMPRegenTime;
        auto it = lastMPRegenTime.find(player->GetCharName());
        if (it == lastMPRegenTime.end() || currentTime - it->second >= 5000) { // 5秒恢复一次
            int regenAmount = player->GetMaxMP() / 100; // 恢复1%
            if (regenAmount > 0) {
                // 使用SetMP方法恢复魔法值
                WORD newMP = std::min(static_cast<WORD>(player->GetMP() + regenAmount), player->GetMaxMP());
                player->SetMP(newMP);
                lastMPRegenTime[player->GetCharName()] = currentTime;
            }
        }
    }
}

void UserEngine::CheckPlayerMagicCooldown(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player || !m_magicManager) return;

    // 检查魔法冷却时间
    // 这里应该与MagicManager配合，检查各个魔法的冷却状态

    // 获取玩家的魔法列表
    const auto& magics = player->GetMagics();
    for (const auto& magic : magics) {
        // 检查每个魔法的冷却时间
        // 具体实现需要MagicManager的支持
    }
}

void UserEngine::ProcessPlayerMagicEffects(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player || !m_magicManager) return;

    // 处理持续性魔法效果
    // 如护身符、魔法盾等持续性效果

    // 检查玩家身上的BUFF和DEBUFF
    // 这里需要与状态系统配合
}

void UserEngine::CheckPlayerMPRegeneration(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player) return;

    // 检查魔法值恢复
    if (player->GetMP() < player->GetMaxMP()) {
        // 根据玩家属性计算恢复速度
        int regenRate = 1000; // 默认1秒恢复一次

        // 根据装备和属性调整恢复速度
        // 这里可以检查装备的魔法恢复属性

        static std::unordered_map<std::string, DWORD> lastRegenTime;
        auto it = lastRegenTime.find(player->GetCharName());
        if (it == lastRegenTime.end() || currentTime - it->second >= regenRate) {
            int regenAmount = std::max(1, player->GetMaxMP() / 200); // 恢复0.5%
            WORD newMP = std::min(static_cast<WORD>(player->GetMP() + regenAmount), player->GetMaxMP());
            player->SetMP(newMP);
            lastRegenTime[player->GetCharName()] = currentTime;
        }
    }
}

// 任务相关辅助方法实现
bool UserEngine::CheckQuestCompletion(std::shared_ptr<PlayObject> player, const QuestData* questData) {
    if (!player || !questData || !m_questManager) return false;

    // 获取玩家的任务状态
    PlayerQuestStatus* questStatus = m_questManager->GetPlayerQuest(player.get(), questData->questId);
    if (!questStatus) return false;

    // 检查所有任务目标是否完成
    for (const auto& objective : questStatus->objectives) {
        if (!objective.completed || objective.currentCount < objective.requiredCount) {
            return false;
        }
    }

    return true;
}

void UserEngine::CheckQuestTimeouts(std::shared_ptr<PlayObject> player) {
    if (!player || !m_questManager) return;

    // 获取玩家任务列表
    auto playerQuests = m_questManager->GetPlayerQuests(player.get());
    DWORD currentTime = GetCurrentTime();

    for (auto& questStatus : playerQuests) {
        if (questStatus.state == QuestState::ACCEPTED) {
            // 获取任务数据
            const QuestData* questData = m_questManager->GetQuest(questStatus.questId);
            if (questData && questData->timeLimit > 0) {
                // 检查任务是否超时
                if (currentTime - questStatus.acceptTime > questData->timeLimit * 1000) {
                    // 任务超时，标记为失败
                    questStatus.state = QuestState::FAILED;
                    player->SendMessage("任务 " + questData->name + " 已超时失败", 2);
                }
            }
        }
    }
}

// 环境相关辅助方法实现
void UserEngine::UpdatePlayerViewRange(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player) return;

    // 更新玩家视野范围
    // 这里可以根据玩家的装备、技能等调整视野范围

    // 检查是否需要更新视野
    static std::unordered_map<std::string, DWORD> lastViewUpdateTime;
    auto it = lastViewUpdateTime.find(player->GetCharName());
    if (it == lastViewUpdateTime.end() || currentTime - it->second >= 500) { // 500ms更新一次
        // 更新视野 - 这个逻辑在PlayObject::Run()中处理
        lastViewUpdateTime[player->GetCharName()] = currentTime;
    }
}

void UserEngine::CheckMapEnvironmentEffects(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player) return;

    // 检查地图环境效果
    std::string mapName = player->GetMapName();
    Point pos = player->GetCurrentPos();

    // 检查是否在特殊区域（如毒沼泽、火山等）
    // 这里需要MapManager提供地图环境信息

    // 检查安全区状态
    // 如果在安全区，禁止PK、恢复生命值等

    // 检查经验倍率区域
    // 如果在经验倍率区域，调整经验获得
}

void UserEngine::UpdatePlayerStatusEffects(std::shared_ptr<PlayObject> player, DWORD currentTime) {
    if (!player) return;

    // 更新玩家状态效果（如中毒、BUFF等）
    // 这里应该与状态系统配合

    // 检查中毒状态
    player->CheckPoisonStatus();

    // 检查状态超时
    player->CheckStatusTimeOut();

    // 处理生命值恢复
    if (player->GetHP() < player->GetMaxHP()) {
        static std::unordered_map<std::string, DWORD> lastHPRegenTime;
        auto it = lastHPRegenTime.find(player->GetCharName());
        if (it == lastHPRegenTime.end() || currentTime - it->second >= 3000) { // 3秒恢复一次
            int regenAmount = std::max(1, player->GetMaxHP() / 300); // 恢复约0.33%
            WORD newHP = std::min(static_cast<WORD>(player->GetHP() + regenAmount), player->GetMaxHP());
            player->SetHP(newHP);
            lastHPRegenTime[player->GetCharName()] = currentTime;
        }
    }
}

} // namespace MirServer